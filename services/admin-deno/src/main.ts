import { Application } from "@oak/oak";
import { cors } from "cors";
import { getLogger } from "@std/log";

import config from "./config/index.ts";
import { initializeDatabase } from "./utils/database.ts";
import { initializeRedis } from "./utils/redis.ts";
import { errorHandler } from "./middleware/errorHandler.ts";
import { rateLimitMiddleware } from "./middleware/rateLimit.ts";
import { authMiddleware } from "./middleware/auth.ts";
import { loggingMiddleware } from "./middleware/logging.ts";
import { securityMiddleware } from "./middleware/security.ts";

// Import routes
import healthRoutes from "./routes/health.ts";
import authRoutes from "./routes/auth.ts";
import systemRoutes from "./routes/system.ts";
import usersRoutes from "./routes/users.ts";

const app = new Application();
const logger = getLogger();

// Global error handler
app.addEventListener("error", (evt) => {
  logger.error("Unhandled error:", evt.error);
});

// Security middleware
app.use(securityMiddleware);

// CORS configuration
app.use(cors({
  origin: config.cors.origins,
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
}));

// Rate limiting
app.use(rateLimitMiddleware);

// Logging middleware
if (config.env !== "test") {
  app.use(loggingMiddleware);
}

// API routes
app.use(healthRoutes.routes());
app.use(healthRoutes.allowedMethods());

app.use(authRoutes.routes());
app.use(authRoutes.allowedMethods());

// Protected routes (require authentication)
app.use(authMiddleware);
app.use(systemRoutes.routes());
app.use(systemRoutes.allowedMethods());

app.use(usersRoutes.routes());
app.use(usersRoutes.allowedMethods());

// 404 handler for API routes
app.use(async (ctx, next) => {
  await next();
  if (ctx.response.status === 404 && ctx.request.url.pathname.startsWith("/api/")) {
    ctx.response.status = 404;
    ctx.response.body = {
      error: "API endpoint not found",
      path: ctx.request.url.pathname,
      method: ctx.request.method,
    };
  }
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown
const controller = new AbortController();
const { signal } = controller;

// Handle shutdown signals
["SIGTERM", "SIGINT"].forEach((signal) => {
  addEventListener(signal, () => {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);
    controller.abort();
  });
});

// Start server
async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();
    logger.info("Database connection established");

    // Initialize Redis connection
    await initializeRedis();
    logger.info("Redis connection established");

    // Start HTTP server
    logger.info(`Admin dashboard server starting on port ${config.port}`);
    logger.info(`Environment: ${config.env}`);
    logger.info(`Process ID: ${Deno.pid}`);

    await app.listen({ 
      port: config.port,
      signal,
    });

  } catch (error) {
    logger.error("Failed to start server:", error);
    Deno.exit(1);
  }
}

// Start the server if this file is run directly
if (import.meta.main) {
  await startServer();
}

export default app;
