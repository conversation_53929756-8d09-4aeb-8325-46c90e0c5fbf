import { Context, Middleware } from "@oak/oak";
import { verify } from "djwt";
import { createAuthenticationError, createAuthorizationError } from "./errorHandler.ts";
import { getUserById } from "../utils/database.ts";
import config from "../config/index.ts";

export interface JWTPayload {
  sub: string; // user ID
  email: string;
  role: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

export const authMiddleware: Middleware = async (ctx: Context, next) => {
  const authHeader = ctx.request.headers.get("Authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw createAuthenticationError("Authorization header required");
  }
  
  const token = authHeader.substring(7);
  
  try {
    // Create crypto key for JWT verification
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "<PERSON><PERSON>", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );
    
    // Verify JWT token
    const payload = await verify(token, key) as JWTPayload;
    
    // Validate token claims
    if (payload.iss !== config.jwt.issuer || payload.aud !== config.jwt.audience) {
      throw createAuthenticationError("Invalid token claims");
    }
    
    // Get user from database to ensure they still exist and are active
    const user = await getUserById(payload.sub);
    if (!user || !user.is_active) {
      throw createAuthenticationError("User not found or inactive");
    }
    
    // Check if user is locked
    if (user.locked_until && new Date(user.locked_until) > new Date()) {
      throw createAuthenticationError("Account is temporarily locked");
    }
    
    // Add user info to context state
    ctx.state.user = {
      id: user.id,
      email: user.email,
      role: user.role,
    };
    
    await next();
  } catch (error) {
    if (error.name === "JWTError" || error.name === "JWTExpired") {
      throw createAuthenticationError("Invalid or expired token");
    }
    throw error;
  }
};

export function requireRole(allowedRoles: string[]): Middleware {
  return async (ctx: Context, next) => {
    const user = ctx.state.user;
    
    if (!user) {
      throw createAuthenticationError("Authentication required");
    }
    
    if (!allowedRoles.includes(user.role)) {
      throw createAuthorizationError("Insufficient permissions");
    }
    
    await next();
  };
}

export function requireAdmin(): Middleware {
  return requireRole(["admin", "super_admin"]);
}

export function requireSuperAdmin(): Middleware {
  return requireRole(["super_admin"]);
}
