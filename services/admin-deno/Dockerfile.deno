# Multi-stage Dockerfile for Deno Admin Service
FROM denoland/deno:2.4.0 AS base

# Set working directory
WORKDIR /app

# Create non-root user
RUN groupadd -r deno && useradd -r -g deno deno

# Development stage
FROM base AS development

# Copy configuration files
COPY deno.json ./
COPY import_map.json ./

# Cache dependencies
RUN deno cache --import-map=import_map.json src/main.ts

# Copy source code
COPY . .

# Change ownership to non-root user
RUN chown -R deno:deno /app

USER deno

# Expose port
EXPOSE 3004

# Start development server with watch mode
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--watch", "src/main.ts"]

# Production stage
FROM base AS production

# Set production environment
ENV DENO_ENV=production

# Copy configuration files
COPY deno.json ./

# Copy source code
COPY src/ ./src/

# Cache dependencies and compile
RUN deno cache --import-map=deno.json src/main.ts

# Create necessary directories
RUN mkdir -p logs uploads && \
    chown -R deno:deno /app

USER deno

# Expose port
EXPOSE 3004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD deno run --allow-net --allow-env healthcheck.ts || exit 1

# Start the application
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "src/main.ts"]

# Default target is production
FROM production
